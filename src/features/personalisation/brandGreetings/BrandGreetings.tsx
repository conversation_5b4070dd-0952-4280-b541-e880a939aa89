import { useEffect, useState } from "react";
import styles from "./BrandGreetings.module.scss";
import { useTranslation } from "next-i18next";
import { ApolloError, useQuery } from "@apollo/client";
import Button from "@features/common/button/Button";
import BrandGreetingsAccordion from "./brandGreetingsAccordian/BrandGreetingsAccordian";
import {
  BRAND_FORM_STATE,
  GREETING_COVER_TYPE,
  GREETING_LANGUAGE,
  LOCALES,
} from "@constants/common";
import useApp, { AppContextAction } from "@features/common/common.context";
import { imageBaseUrl } from "@constants/envVariables";
import BrandGreetingsAccordianSkelton from "../contentLoaders/brandGreetingsAccordianSkeleton/BrandGreetingsAccordianSkelton";
import { BRAND_OCCASIONS_QUERY } from "../personalisation.query";
import { GreetingsOccasionInterface } from "../interfaces/greetings.interface";
import BrandHeader from "../brandPersonalize/brandHeader/BrandHeader";
import { SwipeableDrawer } from "@mui/material";

/**
 * @method BrandGreetings
 * @description Greetings slider component
 * @returns
 */
const BrandGreetings = ({
  onContinue,
  onCancel,
}: any): React.JSX.Element => {
  // #. Get translations
  const { t } = useTranslation("common");

  // button status
  const [buttonDisabled, setButtonDisabled] = useState<boolean>(true);

  // #. State to store selected file path
  const [greetingsFilePath, setGreetingsFilePath] = useState("");

  // #. State to store selected static gif
  const [staticGifPath, setStaticGifPath] = useState("");

  // #. State to store selected greeting reference
  const [referenceCode, setReferenceCode] = useState("");

  const {
    state: { card, localeCode },
    dispatch,
  } = useApp();

  const locale = localeCode || "en";

  //#. Set is animated or not state
  const [isAnimated, setIsAnimated] = useState(false);
  const [occasion, setOccasion] = useState<any>({});
  const [language, setLanguage] = useState(locale);
  const [coverType, setCoverType] = useState<string>(
    locale === "ar" ? GREETING_LANGUAGE.ARABIC : GREETING_LANGUAGE.ENGLISH,
  );

  const [isToggledGif, setToggleGif] = useState(false);
  const [isToggle, setToggle] = useState(false);
  const [errorMessage, setErrorMessage] = useState<boolean>(false);

  const errorImage = `${imageBaseUrl}/icons/error-icon.svg`;

  const toggleGif = () => {
    setToggleGif(!isToggledGif);
    isToggledGif
      ? handleType(GREETING_COVER_TYPE.STANDARD)
      : handleType(GREETING_COVER_TYPE.ANIMATED);
  };

  const toggleLanguage = () => {
    setToggle(!isToggle);
  };

  // #. Handle language toggling for greetings
  useEffect(() => {
    let language = GREETING_LANGUAGE.ENGLISH;

    switch (locale) {
      case "ar":
        if (isToggle) {
          language = GREETING_LANGUAGE.ENGLISH;
        } else {
          language = GREETING_LANGUAGE.ARABIC;
        }
        break;
      case "en":
        if (isToggle) {
          language = GREETING_LANGUAGE.ARABIC;
        } else {
          language = GREETING_LANGUAGE.ENGLISH;
        }
        break;
      default:
        language = GREETING_LANGUAGE.ENGLISH;
    }

    handlelanguage(language);
  }, [isToggle, locale]);

  // #. Get all occasions
  const { loading: occasionLoading, data } =
    useQuery<GreetingsOccasionInterface>(BRAND_OCCASIONS_QUERY, {
      variables: {
        gif: isAnimated,
      },
      context: {
        clientName: "greetings",
        headers: {
          "accept-language": language,
        },
      },
      onError: (error: ApolloError) => {
        setErrorMessage(true);
      },
      onCompleted: (response) => {
        setErrorMessage(false);
      },
    });

  const occasionsData = data?.occasions?.edges || [];
  const defaultGenericOccasionCode = "";
  // #. Sort occasions such that default generic occasion should be shown first
  if (card?.formState === BRAND_FORM_STATE.CREATE) {
    [...occasionsData]?.sort((a: any, b: any) => {
      if (a.node.code === defaultGenericOccasionCode) {
        return -1; // Move the default occasion to the beginning
      }
      if (b.node.code === defaultGenericOccasionCode) {
        return 1; // Move the default occasion to the beginning
      }
      return 0; // Keep the order unchanged for other objects
    });
  }

  /**
   * @method dispatchBrandContext
   * @description Dispatch the new value details
   * @param value
   */
  const dispatchBrandContext = (value: object) => {
    // #. migrate with new values
    const newValue = {
      greetingCover: { ...card.greetingCover, ...value },
      isDirty: true,
    };

    // #. Dispatch the changes
    dispatch({ type: AppContextAction.CARD, payload: newValue });
  };

  /**
   * @method handleOcassion
   * @param occasion
   */
  const handleOcassion = (occCode: string) => {
    const occasions = findOccasionByCode(occCode);
    occasions && setOccasion(occasions?.node);
  };

  /**
   * @method findOccasionByCode
   * @param code
   */
  const findOccasionByCode = (code: string) => {
    const occasion = occasionsData?.find((item: any) => {
      return item?.node?.code === code;
    });

    return occasion;
  };

  /**
   * @method handleType
   * @param event
   */
  const handleType = (value: string) => {
    setCoverType(value);

    // #. Update the gif or not state
    setIsAnimated(value === GREETING_COVER_TYPE.ANIMATED);
  };

  /**
   * @method handlelanguage
   * @param event
   */
  const handlelanguage = (value: string) => {
    setLanguage(value);
  };

  useEffect(() => {
    if (card?.formState === BRAND_FORM_STATE.EDIT) {
      setButtonDisabled(false);
    } else {
      setButtonDisabled(!!!card?.greetingCover?.filePath);
    }
  }, []);
  /**
   * @method onGreetingSelected
   * @param value
   */
  const onGreetingSelected = (value: any) => {
    setButtonDisabled(value.filePath == null);
    setGreetingsFilePath(value.filePath);
    setStaticGifPath(value.staticGifPath);
    setReferenceCode(value.referenceCode);
    if (card?.formState === BRAND_FORM_STATE.CREATE) {
      // #. Dispatch the changes
      dispatchBrandContext({
        filePath: value.filePath,
        referenceCode: value.referenceCode,
        staticGifPath: value.staticGifPath,
        occasion: value.occasion || "",
        language,
        coverType,
      });
    }
  };

  const proceedToNextStep = () => {
    onContinue();
  };
  /**
   * @method onContinueClicked
   */
  const onContinueClicked = () => {
    if (card?.formState === BRAND_FORM_STATE.EDIT && !!greetingsFilePath) {
      // #. Dispatch the changes
      dispatchBrandContext({
        filePath: greetingsFilePath,
        referenceCode: referenceCode,
        staticGifPath: staticGifPath,
        occasion,
        language,
        coverType,
      });
    }
    proceedToNextStep();
  };

  useEffect(() => {
    setLanguage(card?.greetingCover?.language || locale);

    setOccasion(
      locale === LOCALES.ENGLISH
        ? card?.greetingCover?.occasion
        : { code: "BIR", name: "عيد الميلاد" },
    );
    setCoverType(card?.greetingCover?.coverType || "");

    // #. Scroll the window to top
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, []);

  useEffect(() => {
    if (
      (card?.formState === BRAND_FORM_STATE.EDIT &&
        card?.greetingCover?.occasion?.code &&
        !card?.greetingCover?.occasion?.name) ||
      card?.greetingCover?.occasion?.code
    ) {
      const occasion = findOccasionByCode(
        card?.greetingCover?.occasion?.code,
      );
      setOccasion(occasion?.node);
      setButtonDisabled(false);
    }
  }, [occasionLoading]);

  // #. Set gif toggled autopopulated on brand edit flow
  useEffect(() => {
    if (
      (card?.formState === BRAND_FORM_STATE.EDIT &&
        card?.greetingCover?.coverType === GREETING_COVER_TYPE.ANIMATED &&
        isToggledGif === false) ||
      (card?.greetingCover?.coverType === GREETING_COVER_TYPE.ANIMATED &&
        isToggledGif === false)
    ) {
      toggleGif();
    }
  }, []);

  // #. Set language toggled autopopulated on brand edit flow
  useEffect(() => {
    if (
      (card?.formState === BRAND_FORM_STATE.EDIT &&
        card?.greetingCover?.language != locale &&
        card?.greetingCover?.language != "" &&
        isToggle === false) ||
      (card?.greetingCover?.language != locale &&
        card?.greetingCover?.language != "" &&
        isToggle === false)
    ) {
      toggleLanguage();
    }
  }, []);

  const onSkipClicked = () => {
    dispatchBrandContext({
      filePath: "",
      referenceCode: "",
      staticGifPath: "",
      occasion: "",
      language,
      coverType: GREETING_COVER_TYPE.STANDARD,
    });
    proceedToNextStep();
  };

  // State for controlling the confirm modal drawer
  const [confirmModal, setConfirmModal] = useState(false);

  /**
   * @method handleOccasionClick
   * @description Handle clicking on an occasion in the drawer to scroll to corresponding accordion
   * @param occasionCode - The code of the selected occasion
   */
  const handleOccasionClick = (occasionCode: string) => {
    const occasionIndex = occasionsData?.findIndex(
      (item: any) => item?.node?.code === occasionCode,
    );

    if (occasionIndex !== -1) {
      setConfirmModal(false);
      setTimeout(() => {
        const accordionElement = document.querySelector(
          `[data-occasion-code="${occasionCode}"]`,
        );

        if (accordionElement) {
          accordionElement.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 300);
    }
  };

  useEffect(() => {
    if (card?.greetingCover?.filePath) {
      handleOccasionClick(card?.greetingCover?.occasion?.code || "");
    }
  }, [occasionLoading]);
  return (
    <>
      <BrandHeader
        title={"Add Greeting"}
        actionLabel={t("skip")}
        action={onSkipClicked}
        onBack={onCancel}
      />
      <div
        className={`${styles["greetings"]} brand-accordion`}
        data-testid="greetingsChildren"
      >
        <div className={styles["greetings-container"]}>
          <div
            className={`greetings ${styles["greetings-container__top-container"]}`}
          >
            <>
              <div>
                {card?.greetingCover?.coverType && (
                  <button
                    className={`${styles["toggle-buttons"]} ${
                      (card?.greetingCover?.coverType ===
                        GREETING_COVER_TYPE.ANIMATED &&
                        isToggledGif) ||
                      (card?.greetingCover?.coverType ===
                        GREETING_COVER_TYPE.STANDARD &&
                        isToggledGif === true)
                        ? styles["on"]
                        : styles["off"]
                    }`}
                    onClick={toggleGif}
                  >
                    {t("animated")}
                  </button>
                )}
                {card?.formState === BRAND_FORM_STATE.EDIT ? (
                  <button
                    className={`${styles["toggle-buttons"]} ${
                      (card?.greetingCover?.language === "en" ||
                        card?.greetingCover?.language === "ar") &&
                      isToggle
                        ? styles["on"]
                        : styles["off"]
                    }`}
                    onClick={toggleLanguage}
                  >
                    {locale === LOCALES.ENGLISH
                      ? t("arabic")
                      : t("english")}
                  </button>
                ) : (
                  <button
                    className={`${styles["toggle-buttons"]}
                                        ${
                                          isToggle
                                            ? styles["on"]
                                            : styles["off"]
                                        }`}
                    onClick={toggleLanguage}
                  >
                    {locale == "en" ? (
                      <>{t("arabic")} </>
                    ) : (
                      <>{t("english")}</>
                    )}
                  </button>
                )}
              </div>
              <button
                className={`${styles["occasion-buttons"]}`}
                onClick={() => setConfirmModal(true)}
              >
                <p>Occasions</p>
                <img
                  src={`${imageBaseUrl}/icons/search.svg`}
                  alt="search"
                  height={12}
                  width={12}
                />
              </button>
            </>
          </div>
        </div>
        {errorMessage && (
          <>
            <span className={styles["error-container"]}>
              <img src={errorImage} alt="Error" />
              <p className={styles["error-text"]}>{t("error-text")}</p>
            </span>
          </>
        )}
        {occasionLoading && <BrandGreetingsAccordianSkelton />}
        {occasionsData?.length === 0 ? (
          <p className={styles["empty-container"]}>{t("gretingCover")}</p>
        ) : (
          <div className={styles["brand-greetings-accordion"]}>
            {occasionsData?.map((item: any, index: number) => (
              <div key={index} data-occasion-code={item?.node?.code}>
                <BrandGreetingsAccordion
                  gif={isAnimated}
                  onGreetingSelected={onGreetingSelected}
                  handleOcassion={handleOcassion}
                  occasionData={item?.node}
                  occCode={item?.node?.code}
                  isAnimated={isAnimated}
                  language={language}
                  index={index}
                  isToggledGif={isToggledGif}
                  occasionsArray={
                    occasionsData?.map(
                      (occasion: any) => occasion?.node?.code,
                    ) || []
                  }
                />
              </div>
            ))}
          </div>
        )}
      </div>
      <div className="brand-button">
        {card?.greetingCover?.filePath && (
          <img
            src={card?.greetingCover?.filePath}
            alt="atwork-logo"
            className="selected-image"
          />
        )}
        <Button
          theme="at-work-primary"
          action={onContinueClicked}
          className={`brand-button__continue ${styles["brand-button__continue"]}`}
          attribues={{
            disabled: buttonDisabled,
            type: "button",
          }}
        >
          {t("continue")}
        </Button>
      </div>
      <SwipeableDrawer
        className={"personalisation-drawer"}
        anchor={"bottom"}
        open={confirmModal}
        onClose={() => setConfirmModal(false)}
        onOpen={() => {}}
      >
        <div className={styles["confirm-modal__wrapper"]}>
          <div className={styles["confirm-modal__header"]}>
            <div className={styles["confirm-modal__line"]}></div>
            <h5>{t("selectOccasion")}</h5>
          </div>

          <div className={styles["confirm-modal__body"]}>
            {occasionsData?.map((item: any, index: number) => (
              <p
                key={index}
                onClick={() => handleOccasionClick(item?.node?.code)}
              >
                {item?.node?.name}
              </p>
            ))}
          </div>
        </div>
      </SwipeableDrawer>
    </>
  );
};
export default BrandGreetings;
